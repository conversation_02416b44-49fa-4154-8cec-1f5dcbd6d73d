[build-system]
requires = ["setuptools>=61.0.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "speech-cli"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "langchain-core>=0.3.72",
    "langchain-google-genai>=2.1.8",
    "langgraph>=0.6.2",
    "python-dotenv>=1.1.1",
    "textual-autocomplete>=4.0.5",
    "textual[syntax]>=5.2.0",
]

[project.scripts]
speech = "speech_cli.cli:main"

[tool.setuptools.packages.find]
where = ["."]
include = ["speech_cli*"]

[dependency-groups]
dev = [
    "pre-commit>=4.2.0",
    "textual-dev>=1.7.0",
]

