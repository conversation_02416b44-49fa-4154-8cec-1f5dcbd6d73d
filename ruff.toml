target-version = "py313"
extend-exclude = []
show-fixes = true

[lint]
select = [
    # pycodestyle
    "E",
    # Pyflakes
    "F",
    # pyupgrade
    "UP",
    # flake8-bugbear
    "B",
    # flake8-simplify
    "SIM",
    # isort
    "I",
    "C4",
    "A",
    "DJ",
    "FA",
    "COM818",
    "ISC",
    "ICN",
    "LOG",
    "G",
    "T20",
    "PT",
    "PTH",
    "N",
    "PERF",
    "W",
    "D",
]
ignore = ["F403", "F401", "D100", "D104", "D105", "D106", "D102",  "D401", "D107"]
[lint.per-file-ignores]

[format]
docstring-code-format = true
docstring-code-line-length = "dynamic"