import logging
from collections.abc import Coroutine
from logging.config import dictConfig

from langchain_core.messages import AIMessageChunk, ToolMessage
from langgraph.types import Interrupt
from rich.console import Console
from rich.prompt import Prompt
from textual import work
from textual.app import App, ComposeR<PERSON>ult
from textual.containers import <PERSON><PERSON><PERSON>, Horizontal, VerticalScroll
from textual.widgets import <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, In<PERSON>, <PERSON><PERSON>, <PERSON>, St<PERSON>
from textual_autocomplete import AutoComplete

from .agents import Generator, Translator
from .config import LOGGING_CONFIG, config
from .widgets import AgentResponse

dictConfig(LOGGING_CONFIG)

logger = logging.getLogger(__name__)


class SpeechCLI(App):
    """Speech CLI tool."""

    BINDINGS = [("d", "toggle_dark", "Toggle dark mode")]
    CSS_PATH = "styles.tcss"

    _models = [
        "gemini-2.5-pro",
        "gemini-2.5-flash",
        "gemini-2.5-flash-lite",
    ]

    _generator = Generator()
    _translator = Translator()
    _agents_execution_node: Coroutine | None = None

    def generate_prd(self):
        """Generate PRD file from HLC."""
        pass

    def update_agent_response_widget(
        self,
        agent_response_widget: AgentResponse,
        agent_response: AIMessageChunk | ToolMessage | Interrupt,
    ) -> bool:
        """Update the widget with the content."""
        if isinstance(agent_response, AIMessageChunk):
            if agent_response_widget.create_new_ai_message_widget:
                # Reseting the ai message to an empty string.
                agent_response_widget.ai_message = ""

            agent_response_widget.ai_message += agent_response.content
        elif isinstance(agent_response, ToolMessage):
            agent_response_widget.tool_response = agent_response
        elif isinstance(agent_response, Interrupt):
            agent_response_widget.human_feedback = agent_response

            return False
        elif isinstance(agent_response, str):
            pass

        return True

    async def run_translator(
        self, agent_response_widget: AgentResponse, user_input: str | None = None
    ) -> None:
        """Execute translator agent."""
        self._agents_execution_node = self.run_translator
        async for agent_response in await self._translator.run(
            initial_state=user_input
        ):
            self.update_agent_response_widget(agent_response_widget, agent_response)

        self._agents_execution_node = None

    async def run_generator(
        self, agent_response_widget: AgentResponse, user_input: str
    ) -> Coroutine | None:
        """Execute generator agent.

        Returns True if the generator agent responded, False otherwise.

        Args:
            user_input (str): The user input.
            agent_response_widget (AgentResponse): The widget to update.

        Returns:
            bool: Whether the widget was updated or not, i.e. the agent responded

        """
        self._agents_execution_node = self.run_generator
        async for agent_response in await self._generator.run(initial_state=user_input):
            next_agent = self.update_agent_response_widget(
                agent_response_widget, agent_response
            )

        # if next_agent:
        #     return await self.run_translator(agent_response_widget)

    @work(exclusive=True, exit_on_error=False)
    async def agents_executor(self, user_input: str):
        """Executes translator and generator agents asynchronously as a worker.

        Serves as the bridge between the agents and ui.
        """
        self.chat_box.value = ""
        user_message = Horizontal(
            Static(content=user_input, classes="userMessage"),
            classes="userMessageContainer",
        )
        self.chat_area.mount(user_message)
        self.chat_area.scroll_end()

        if user_input == "/prd":
            return self.generate_prd()

        agent_response_widget = AgentResponse()
        self.chat_area.mount(agent_response_widget)
        self.chat_area.scroll_end()

        try:
            await self.run_generator(agent_response_widget, user_input)
            self.chat_area.scroll_end()
        except ConnectionError as e:
            agent_response_widget.connection_error = True
            logger.error("Connection Error: %s", e)
        except Exception as e:
            agent_response_widget.unknown_error = True
            logger.error("Unknown Exception: %s", e)

    def on_mount(self) -> None:
        self.title = "Speech CLI"
        self.sub_title = "From Natural Language to Code"

    def compose(self) -> ComposeResult:
        """Create child widgets for the app."""
        yield Header()

        with Container():
            self.chat_area = VerticalScroll(id="chatArea")
            yield self.chat_area

            self.chat_box = Input(
                id="chatBox", placeholder="What are we building?", type="text"
            )
            yield self.chat_box

            yield AutoComplete(self.chat_box, candidates=["/code", "/prd"])

        yield Footer()

    def action_toggle_dark(self) -> None:
        """An action to toggle dark mode."""
        self.theme = (
            "textual-dark" if self.theme == "textual-light" else "textual-light"
        )

    def action_quit(self) -> None:
        """Cancel all workers upon exit from user."""
        for worker in self.app.workers:
            worker.cancel()

        return super().action_quit()

    async def on_input_submitted(self, event: Input.Submitted):
        """Handle user input."""
        if event.value.isspace() or len(event.value) == 0:
            return

        self.agents_executor(event.value)

    def select_model(self):
        """Select model and api key from user."""
        # TODO: Make this functional.
        console = Console()
        for model in self._models:
            console.print(f"{self._models.index(model) + 1.0}[bold] {model}[/bold]")
        model = Prompt.ask("Pick your model: ")

        api_key = Prompt.ask("Enter your API key: ")
        config.model = model
        config.api_key = api_key
        config.save("model", model)
        config.save("api_key", api_key)

        return model, api_key

    def run(self):
        """Verify configuration before running app."""
        # FIXME: Ensure that the user authenticates.
        # if not config.model and not config.api_key:
        #     config.model, config.api_key = self.select_model()

        return super().run()


def main():
    """Instantiate and run the speech cli app."""
    app = SpeechCLI()
    app.run()


if __name__ == "__main__":
    main()
