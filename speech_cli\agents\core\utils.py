from pathlib import Path
from typing import Any

from langchain_core.prompt_values import PromptValue
from langchain_core.prompts import ChatPromptTemplate

system_template = "Translate the following from English into {language}"


def create_prompt(system_message: str, *args: str, **kwargs: Any) -> PromptValue:
    """Create a formatted prompt.

    Pass in the system message and a series of messages, you would want to make the user
    message. And specify the formatters via kwargs.

    Args:
        system_message (str): The system message for the model.
        *args (str): Messages that will form the user message.
        **kwargs (Any): The formatters.

    Returns:
        PromptValue: The formatted prompt.

    """
    prompt_template = ChatPromptTemplate.from_messages(
        [("system", system_message), ("human", args.join("\n"))]
    )

    return prompt_template.invoke(kwargs)


def get_hlc_document():
    """Generate the hlc prompt from the available HLC.json file."""
    # TODO: Implement this to retrieve the hlc document.
    return "Empty as the moment."
