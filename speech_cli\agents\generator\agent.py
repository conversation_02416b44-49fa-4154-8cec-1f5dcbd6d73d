import logging
from typing import Any

from langchain_core.messages import SystemMessage
from langgraph.checkpoint.memory import InMemorySaver
from langgraph.graph import END, START, StateGraph

from speech_cli.agents import core
from speech_cli.agents.core import system_messages

from .states import OverallState

logger = logging.getLogger(__name__)


class Generator(core.BaseAgent):
    """Agent for generating hlc code from natural language."""

    overall_state = OverallState

    config = {"recursion_limit": 50, "configurable": {"thread_id": "generator_1"}}

    checkpointer = InMemorySaver()

    _system_message = [SystemMessage(content=system_messages.generator)]

    @property
    def nodes(self):
        return [
            ("generator", self.generator_node),
        ]

    @property
    def entry_point(self):
        """Entry point to the translator agent."""
        return self.generate

    async def generator_node(self, state: OverallState) -> OverallState:
        """Translates HLC into high level programming languages."""
        messages = self._system_message + state.messages

        response = await self.invoke_llm(messages)

        return {"messages": response}

    def add_edges(self, builder) -> StateGraph:
        """Adds edges to builder."""
        builder.add_edge(START, "generator")
        builder.add_edge("generator", END)

        return builder

    async def generate(self, initial_state: str | dict[str, Any]):
        """Async entry point to generator agent."""
        async for token, _ in self.graph.astream(
            self.build_graph_input(initial_state),
            config=self.config,
            stream_mode="messages",
            # stream_mode=["updates", "messages", "custom"],
        ):
            logger.debug("In the generator agent, token: %r", token)
            yield token
        yield self.get_interrupt()
