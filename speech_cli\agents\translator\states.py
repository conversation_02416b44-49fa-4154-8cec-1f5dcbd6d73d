from typing import Annotated

from langchain_core.messages import AnyMessage
from langgraph.graph import add_messages
from pydantic import BaseModel, <PERSON>


def add_actions(existing: list[str], current: list[str]) -> dict:
    """Add actions to the state."""
    if not existing:
        existing = []

    if not current:
        current = []

    return existing + current


class OverallState(BaseModel):
    """The overall state.

    Contains all the necessary state keys.
    """

    messages: Annotated[list[AnyMessage], add_messages] = Field(
        description="The list of messages."
    )
    # actions: Annotated[list[str] | None, add_actions] = Field(
    #     "List of concise summaries of the ai actions. Generated before each action is"
    #     " taken. An action refers to making a tool call or ending the process."
    # )
