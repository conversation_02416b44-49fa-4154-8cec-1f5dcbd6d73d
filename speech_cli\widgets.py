from langchain_core.messages import ToolMessage
from langgraph.types import Interrupt
from textual.app import ComposeResult
from textual.containers import Container, Horizontal, Vertical
from textual.reactive import reactive
from textual.widget import Widget
from textual.widgets import <PERSON><PERSON>, <PERSON><PERSON>, Pretty, Static


class ToolExecution(Static):
    """Tool response widget for displaying tool execution."""

    def compose(self) -> ComposeResult:
        """Create child widgets for the app."""


class GraphInterrupt(Container):
    """Graph interrupt widget.

    For accepting, editing, directly responding or rejecting tool execution.
    """

    def compose(self) -> ComposeResult:
        """Create child widgets for the app."""
        yield Pretty()
        with Horizontal():
            yield Button("Accept")
            yield Button("Reject")


class AgentResponse(Vertical):
    """An agent response widget for displaying all possible agent responses."""

    ai_message: str = reactive("", init=False, layout=True)
    ai_message_widget: Markdown | None = None
    create_new_ai_message_widget: bool = True
    tool_response: ToolMessage | None = reactive(None, init=False, layout=True)
    human_feedback: Interrupt | None = reactive(None, init=False, layout=True)
    connection_error: bool = reactive(False, init=False, layout=True)
    unknown_error: bool = reactive(False, init=False, layout=True)

    def on_mount(self) -> None:
        self.loading = True

    def _ensure_widget_ready(self, create_new_ai_message_widget=False):
        """Remove loading indicator & ensure the ai message goes to the right widget."""
        if self.loading:
            self.loading = False

        self.create_new_ai_message_widget = create_new_ai_message_widget

    async def watch_ai_message(self, ai_message: str) -> None:
        if self.create_new_ai_message_widget:
            self.ai_message_widget = Markdown(markdown=ai_message, classes="aiMessage")
            self._ensure_widget_ready()
            await self.mount(self.ai_message_widget)
        else:
            self.ai_message_widget.update(ai_message)

    async def watch_tool_response(self, tool_response: ToolMessage) -> None:
        self._ensure_widget_ready(True)
        await self.mount(Static("Testing tool response!"))

    async def watch_human_feedback(self, human_feedback: Interrupt) -> None:
        self._ensure_widget_ready(True)
        await self.mount(Static("Testing human feedback!"))

    async def watch_connection_error(self, connection_error: bool) -> None:
        self._ensure_widget_ready()

        await self.mount(Static("Connection error!"))

    async def watch_unknown_error(self, unknown_error: bool) -> None:
        self._ensure_widget_ready()

        await self.mount(Static("Unknown error!"))
