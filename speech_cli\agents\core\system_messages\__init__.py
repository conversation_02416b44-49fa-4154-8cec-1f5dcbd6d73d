from pathlib import Path


class _SystemMessages:
    """Retrieve the system message.

    Make sure the system message is stored in .txt file.
    And that the attribute name matches the file name.

    Example:
        >>> SystemMessages.promptor  # returns PROMPTOR.md

    """

    current_dir = Path(__file__).parent

    def _load_file(self, file: Path):
        """Extract raw text from a markdown file, removing all formatting.

        Args:
            file: Path to the markdown file

        Returns:
            str: Plain text content without markdown formatting

        """
        with file.open(encoding="utf-8") as f:
            content = f.read()

        return content

    def __getattr__(self, name: str):
        file = self.current_dir / f"{name.upper()}.md"

        if not file.exists():
            raise FileNotFoundError(
                "No file named {name}.md found in the system messages directory."
            )

        return self._load_file(file)


system_messages = _SystemMessages()
