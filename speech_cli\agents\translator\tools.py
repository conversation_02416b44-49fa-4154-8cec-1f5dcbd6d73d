# ruff: noqa: F841
import logging
import os
import subprocess
from pathlib import Path

from langgraph.types import interrupt

logger = logging.getLogger(__name__)


def terminal_use(command: str) -> tuple[bool, str, str]:
    """Execute a command in the terminal and return the output.

    Args:
        command: The command to execute
        cwd: Working directory to run the command in. Defaults to current directory.

    Returns:
        tuple[bool, str, str]: (success, stdout, stderr)

    """
    try:
        # Use current working directory if none specified
        working_dir = Path.cwd()

        # Ensure the directory exists
        if not working_dir.exists():
            return False, "", f"Directory does not exist: {working_dir}"

        # Run the command
        result = subprocess.run(
            command,
            shell=True,
            cwd=working_dir,
            capture_output=True,
            text=True,
            timeout=30,  # 30 second timeout
        )

        return result.returncode == 0, result.stdout, result.stderr

    except subprocess.TimeoutExpired:
        return False, "", "Command timed out after 30 seconds"
    except Exception as e:
        return False, "", f"Error executing command: {str(e)}"


async def write_file(file_path: str, content: str) -> tuple[bool, str]:
    """Create and write content to a file.

    Args:
        file_path: Relative path to the file from current working directory
        content: Content to write to the file
        encoding: File encoding (default: utf-8)

    Returns:
        tuple[bool, str]: (success, message)

    """
    logger.debug("About to write to file: %s", file_path)
    # response = interrupt("Who are you")
    try:
        # Convert to Path object and resolve relative to current working directory
        target_path = Path.cwd() / file_path

        # Create parent directories if they don't exist
        target_path.parent.mkdir(parents=True, exist_ok=True)

        # Write content to file
        with target_path.open("w", encoding="utf-8") as f:
            f.write(content)

        return True, f"Successfully wrote to {target_path}"

    except PermissionError:
        return False, f"Permission denied: Cannot write to {file_path}"
    except OSError as e:
        return False, f"OS error writing to {file_path}: {str(e)}"
    except Exception as e:
        return False, f"Unexpected error writing to {file_path}: {str(e)}"
