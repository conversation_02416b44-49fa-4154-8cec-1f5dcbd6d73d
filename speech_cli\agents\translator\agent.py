import logging
from pathlib import Path
from typing import Any

from langchain_core.messages import HumanMessage, SystemMessage
from langgraph.checkpoint.memory import InMemorySaver
from langgraph.graph import START, StateGraph
from langgraph.prebuilt import ToolNode, tools_condition

from speech_cli.agents import core
from speech_cli.agents.core import system_messages
from speech_cli.agents.core.decorators import add_human_in_the_loop

from .states import OverallState
from .tools import terminal_use, write_file

logger = logging.getLogger(__name__)


class Translator(core.BaseAgent):
    """Agent for translating hlc code to high level languages."""

    overall_state = OverallState

    tools = [
        add_human_in_the_loop(terminal_use),
        add_human_in_the_loop(write_file),
    ]

    config = {"recursion_limit": 50, "configurable": {"thread_id": "translator_2"}}

    checkpointer = InMemorySaver()

    _system_message = [SystemMessage(content=system_messages.translator)]

    @property
    def nodes(self):
        return [
            ("translator", self.translator_node),
            ("tools", ToolNode(self.tools)),
        ]

    @property
    def entry_point(self):
        """Entry point to the translator agent."""
        return self.translate

    async def translator_node(self, state: OverallState) -> OverallState:
        """Translates HLC into high level programming languages."""
        messages = self._system_message + state.messages

        response = await self.invoke_llm(messages)

        return {"messages": response}

    def add_edges(self, builder) -> StateGraph:
        """Adds edges to builder."""
        builder.add_edge(START, "translator")
        builder.add_conditional_edges("translator", tools_condition)
        builder.add_edge("tools", "translator")

        return builder

    def _get_hlc(self) -> str:
        """Get the HLC.json file in the working directory."""
        file = Path.cwd() / "HLC.json"

        with file.open(encoding="utf-8") as f:
            content = f.read()

        return content

    async def translate(self, initial_state: str | dict[str, Any]):
        """Async entry point to translator agent."""
        if not initial_state:
            initial_state = self._get_hlc()

        async for token, metadata in self.graph.astream(
            self.build_graph_input(initial_state),
            config=self.config,
            stream_mode="messages",
            # stream_mode=["updates", "messages", "custom"],
        ):
            logger.debug(
                "The current translator graph outputs - token: %r, metadata: %r",
                token,
                metadata,
            )
            yield token
        yield self.get_interrupt()
