[{"name": "DJANGO-<PERSON><PERSON><PERSON>", "type": "entry_level", "prompt": "Create this project using django style project structure.", "linkers": [{"name": "rules", "description": "The rules to use for this project.", "value": {"name": "DJANGO-<PERSON><PERSON>LE-<PERSON><PERSON><PERSON>", "type": "directive", "prompt": "Separate the settings file into local, production and testing."}}, {"name": "venv-manager", "description": "The virtual environment manager to use.", "value": {"name": "UV", "type": "directive", "prompt": "Use python uv as the package and project manager."}}, {"name": "framework", "description": "The framework to use for this project.", "value": {"name": "DJANGO", "type": "directive", "prompt": "Use python Django as the framework for this project.", "linkers": [{"name": "version", "description": "The version to use.", "value": {"name": "VERSION", "type": "literal", "prompt": "Use the latest version of Django."}}]}}]}, {"name": "CALCULATOR-APP", "type": "top_level", "prompt": "Create a calculator app.", "linkers": [{"name": "pages", "description": "The pages to define.", "value": {"name": "PAGES", "type": "literal", "prompt": "Define a single page for the calculator."}}]}]