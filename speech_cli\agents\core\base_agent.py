import asyncio
import logging
from collections.abc import AsyncGenerator, Callable, Coroutine
from typing import Any

from langchain_core.language_models import LanguageModelInput
from langchain_core.messages import BaseMessage, HumanMessage
from langchain_core.runnables import Runnable
from langchain_google_genai import ChatGoogleGenerativeAI
from langgraph.checkpoint.memory import InMemorySaver
from langgraph.graph import StateGraph
from langgraph.graph.state import CompiledStateGraph
from langgraph.types import Command, Interrupt

from speech_cli.config import config

logger = logging.getLogger(__name__)


class BaseAgent:
    """Base agent class for generator and translator agents."""

    overall_state: Any | None = None
    input_schema: Any | None = None
    output_schema: Any | None = None
    tools: list[Callable] | None = None
    graph: CompiledStateGraph | None = None
    llm: ChatGoogleGenerativeAI | Runnable[LanguageModelInput, BaseMessage] | None = (
        None
    )
    config: dict[str, Any] | None = None
    checkpointer: InMemorySaver | None = None

    def _build_llm_with_tools(
        self,
    ) -> ChatGoogleGenerativeAI | Runnable[LanguageModelInput, BaseMessage]:
        """Builds the llm with tools if available.

        Returns:
            ChatGoogleGenerativeAI: The llm model.

        """
        llm = ChatGoogleGenerativeAI(
            model=config.model,
            api_key=config.api_key,
        )
        llm_with_tools = llm.bind_tools(self.tools) if self.tools else llm

        return llm_with_tools

    async def invoke_llm(self, messages):
        timeout = 20.0
        try:
            # ainvoke is async, so we can wrap it directly
            response = await asyncio.wait_for(
                self.llm.ainvoke(messages),
                timeout=timeout,  # hard cap regardless of SDK defaults
            )
        except TimeoutError as err:
            raise ConnectionError(
                f"LLM call timed out after {timeout} seconds."
            ) from err
        return response

    def get_interrupt(self) -> Interrupt | str:
        """Retrieve the interrupt from the graph if available."""
        interrupts = self.graph.get_state(self.config).interrupts

        if len(interrupts) > 0:
            return interrupts[0]

        return ""

    @property
    def get_overall_state(self):
        """Returns the overall state if available.

        Else return a state that inherits from input schema and output schema.
        """
        try:
            if self.overall_state:
                return self.overall_state
            elif self.input_schema and self.output_schema:
                return type("OverallState", (self.input_schema, self.output_schema), {})

            raise AttributeError(
                "Missing overall state attribute, make sure to specify the overall"
                " state or the input and output schema."
            )
        except Exception as err:
            raise err

    def add_edges(self, builder) -> StateGraph:
        raise NotImplementedError("'add_edges' method, is not implemented yet.")

    @property
    def nodes(self) -> list[tuple[str, str]]:
        raise NotImplementedError("Missing nodes property.")

    @property
    def entry_point(self):
        raise NotImplementedError("Missing entry point property.")

    def build_graph_input(
        self, initial_state: str | dict[str, Any]
    ) -> dict[str, list] | Command:
        """Builds the input for intiating graph execution."""
        if isinstance(initial_state, str):
            logger.debug("Creating a HumanMessage type input with, %s", initial_state)

            return {"messages": [HumanMessage(content=initial_state)]}
        elif isinstance(initial_state, dict):
            logger.debug("Creating a Command type input with, %s", initial_state)

            return Command(resume=[initial_state])

        raise Exception("Invalid initial state.")

    def add_nodes(self, builder) -> StateGraph:
        """Adds nodes to builder."""
        try:
            for node_name, node in self.nodes:
                builder.add_node(node_name, node)
        except Exception as err:
            raise err

        return self.add_edges(builder)

    def _build(self) -> CompiledStateGraph:
        """Builds a graph representation of the agent."""
        builder = StateGraph(
            self.get_overall_state,
            input_schema=self.input_schema,
            output_schema=self.output_schema,
        )

        return self.add_nodes(builder).compile(
            checkpointer=self.checkpointer,
        )

    def _setup(self) -> Callable:
        if not self.graph:
            self.graph = self._build()

        if not self.llm:
            self.llm = self._build_llm_with_tools()

        return self.entry_point

    async def run(
        self,
        initial_state: str | list[dict[str, Any]] = None,
    ) -> Coroutine[AsyncGenerator[Any, Any, None]]:
        """Async entry point to every agent."""
        return self._setup()(initial_state)
