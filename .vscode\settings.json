{
  "[python]": {
    "editor.formatOnSave": true,
    "files.autoSave": "afterDelay",
    "editor.defaultFormatter": "charliermarsh.ruff",
    "editor.formatOnPaste": true,
    "editor.formatOnType": false
  },
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.ruff": "explicit",
    "source.organizeImports.ruff": "explicit"
  },
  "autoDocstring.includeExtendedSummary": true,
  "vscode-json-editor.theme": "light",
}
