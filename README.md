[
{
"name": "SCRIPT",
"type": "entry_level",
"prompt": "Create this project using this structure.",
"linkers": {
"rules": {
"name": "SCRIPT-RULE",
"type": "directive",
"prompt": "Create this project structure using this rule.",
}
},
},
{
"name": "DESKTOP-APP",
"type": "top_level",
"prompt": "Create this project using this structure.",
"rules": {
"name": "DESKTOP-APP-RULE",
"type": "directive",
"prompt": "Create this project using this rule.",
"linkers": null,
},
},
]

This is a software specification written in a json format, here is how it is structured. it starts out with an array of two objects, with the first object being an entry level object, used to specify the general program structure, rules, guidelines, and features of the project. the second object is a top level object, that specifies the kind of software to be built, be it a mobile app, a game, etc. these two object always appear in this order, and only occurs once each. the other type of objects are features (feature objects), used to describe the feature a parent object should have, controls (control object), used to describe how the parent object should behave in certain aspect, directives (directive objects), used to describe rules and guidlines, policies, the parent object should abide by, and literals ( literal objects), used to provide direct number or string value, that will be interpreted as it is, to a parent object. features, controls, directives, and literals are nested objects, in the sense that they only appear through linking to a parent object, however objects can't be linked to a literal. top level objects and entry level objects are file level objects, such that they only appear once each, as the first set of two objects that will be encoutered in the tree. this also means, that the top level and entry level objects can not be linked to other objects, not even to themselves, and will always appear at the file level. each object always comprises of three attributes, name, type, and prompt with an optional linkers attribute. the name, is unique and refers to the name of the object, the type, refers to the type of the object, (as in feature, top_level, literal, entry_level, control or directive), and the prompt, refers to a string which gives each object their meaning, and helps to understand what the particular object is requesting to be done, in relationship to the parent objects, where applicable. the linkers is a means of linking other objects to a parent object, and could comprise of many linkers, however, a literal does not have this attribute and it is even optional for all other objects to define this, as defining it means, getting more detailed about how the object should be understood. the linkers, definition has three attributes, the name, which is the name of the linker and the description, which describes how that linker will affect the parent object, and the value, which maps to the object which is being linked. there are two different places where an array can be used those are at the begining of the tree to house the top level and entry level objects, which is compulsory and to enclose the list of linkers of an object. i just described to you a new programming language, called human language code (HLC), that makes use of the .json file format. this new programming language is also reffered to as a software specifications intermediate language, which basically means it sits in between a human natural language, and a high level languge, and houses the specifications of a software written as json objects. Now, describe this language to someone who has no knowledge of it but understands json, such that, that person without your assistance or anyone else assistance will be able to understand and be capable of explaining it to another newbie. make it clear and explanatory.
