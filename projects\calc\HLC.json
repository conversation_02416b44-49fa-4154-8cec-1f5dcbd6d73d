[{"name": "DJANGO-<PERSON><PERSON><PERSON>", "type": "entry_level", "prompt": "Create this project using a clean Django project structure.", "linkers": [{"name": "framework", "description": "Main web framework to use", "value": {"name": "DJANGO", "type": "directive", "prompt": "Use Python Django as the framework.", "linkers": [{"name": "version", "description": "Django release to target", "value": {"name": "LATEST", "type": "literal", "prompt": "Use the latest stable version of Django."}}]}}]}, {"name": "HABIT-TRACKER-APP", "type": "top_level", "prompt": "A minimal daily habit tracker.", "linkers": [{"name": "auth", "description": "User access control", "value": {"name": "LOGIN_REQUIRED", "type": "directive", "prompt": "All views require login; use Django’s built-in auth."}}, {"name": "core_feature", "description": "Primary user capability", "value": {"name": "HABIT_CRUD", "type": "feature", "prompt": "Users can create, list, update and delete personal habits."}}, {"name": "daily_check", "description": "Mark habits complete", "value": {"name": "TOGGLE_RECORD", "type": "feature", "prompt": "One-click toggle to mark/unmark a habit done for today."}}, {"name": "streak_view", "description": "Visual feedback", "value": {"name": "STREAK_DISPLAY", "type": "control", "prompt": "Show current streak length beside each habit in the list."}}]}]