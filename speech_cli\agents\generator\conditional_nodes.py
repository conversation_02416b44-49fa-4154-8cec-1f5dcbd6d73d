# ruff: noqa: F841
import logging
from typing import Any, Literal

from langchain_core.messages import AnyMessage
from pydantic import BaseModel

from .states import OverallState

logger = logging.getLogger(__name__)


def router_node(
    state: OverallState,
) -> Literal["planner", "editor", "__end__"]:
    """Used to route to the orchestrator or __end__ depending on the promptor output.

    Args:
        state: The state to check for
            node calls. Must have the "route" key (StateGraph).

    Returns:
        The next node to route to.

    """
    name = "promptor_conditional_node"

    logger.debug("In the router node, you got, this state, %r", state)
    return state.route


def editor_tools_condition(
    state: list[AnyMessage] | dict[str, Any] | BaseModel,
    messages_key: str = "messages",
) -> Literal["editor_tools", "orchestrator"]:
    """Use in the conditional_edge to route to the ToolNode if the last message.

    has tool calls. Otherwise, route to the orchestrator.

    Args:
        state: The state to check for
            tool calls. Must have a list of messages (MessageGraph) or have the
            "messages" key (StateGraph).
        messages_key: The message key.

    Returns:
        The next node to route to.

    """
    if isinstance(state, list):
        ai_message = state[-1]
    elif (
        isinstance(state, dict)
        and (messages := state.get(messages_key, []))
        or (messages := getattr(state, messages_key, []))
    ):
        ai_message = messages[-1]
    else:
        raise ValueError(f"No messages found in input state to tool_edge: {state}")
    if hasattr(ai_message, "tool_calls") and len(ai_message.tool_calls) > 0:
        return "editor_tools"
    return "orchestrator"
