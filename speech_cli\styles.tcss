MainContent {
    height: 100%;
    width: 100%;
    margin: 1 0;
    padding: 0 0;

}

Input {
    height: 1;
    box-sizing: content-box;
}

AgentResponse {
  max-width: 100%;
  padding: 1;
  height: auto;
  min-height: 4;
  margin-bottom: 1;
  margin-right: 8;
}

LoadingIndicator {
  background: $background;
  width: 100%;
  height: 2;
  content-align: left top;
}

Markdown {
  padding: 0 0;
  # overflow-y: scroll;
}

#chatBox {
  dock: bottom;
  padding-left: 2;
  margin-right: 14;
  margin-left: 10;
}

#chatArea {
  background: $background;
  margin: 1 1;
  margin-bottom: 1;
  margin-left: 10;
  scrollbar-gutter: stable;
  scrollbar-size-vertical: 2;
}

.userMessageContainer {
  width: 100%;
  align: right top;
  height: auto;
  margin-right: 8;
}

.userMessage {
  background: $surface;
  max-width: 60%;
  width: auto;
  padding: 1;
  height: auto;
  margin-bottom: 1;
}

# .aiMessage {
#   # display: none;
# }

# .started #stop {
#   display: block;
# }

# .started #reset {
#   visibility: hidden;
# }
