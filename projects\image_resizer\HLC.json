[{"name": "REACT-STYLE", "type": "entry_level", "prompt": "Create this project using React-based project structure.", "linkers": [{"name": "rules", "description": "The project structure and configuration rules.", "value": {"name": "REACT-STRUCTURE-RULE", "type": "directive", "prompt": "Use Create React App for project setup. Split configuration into local, production, and testing files."}}, {"name": "framework", "description": "The frontend framework to use.", "value": {"name": "REACT", "type": "directive", "prompt": "Use React.js as the frontend framework.", "linkers": [{"name": "version", "description": "React version to use.", "value": {"name": "REACT_VERSION", "type": "literal", "prompt": "Use React version 18.x for this project."}}]}}, {"name": "backend", "description": "The backend technology.", "value": {"name": "NODE-EXPRESS", "type": "directive", "prompt": "Use Node.js with Express for handling image processing and API requests.", "linkers": [{"name": "storage", "description": "Image storage solution.", "value": {"name": "CLOUD_STORAGE", "type": "directive", "prompt": "Integrate cloud storage (e.g., AWS S3 or Google Cloud Storage) for storing resized images.", "linkers": [{"name": "access", "description": "Storage access configuration.", "value": {"name": "ACCESS_CONFIG", "type": "literal", "prompt": "Configure secure access keys and permissions for cloud storage."}}]}}]}}]}, {"name": "IMAGE-RESIZE-APP", "type": "top_level", "prompt": "Create an image resize web application.", "linkers": [{"name": "features", "description": "Key features of the application.", "value": {"name": "FEATURES", "type": "feature", "prompt": "Implement the following features:", "linkers": [{"name": "upload", "description": "Image upload system.", "value": {"name": "FILE_UPLOAD", "type": "feature", "prompt": "Allow users to upload images via drag-and-drop or file picker.", "linkers": [{"name": "library", "description": "Upload library to use.", "value": {"name": "FILEPOND", "type": "directive", "prompt": "Integrate FilePond for handling image uploads with progress tracking and validation."}}]}}, {"name": "resize", "description": "Image resizing functionality.", "value": {"name": "RESIZE_OPTIONS", "type": "feature", "prompt": "Provide multiple resize options (e.g., pixels, percentage, predefined sizes).", "linkers": [{"name": "backend", "description": "Backend resize processing.", "value": {"name": "SHARP_LIBRARY", "type": "directive", "prompt": "Use Sharp library on the server for efficient image resizing."}}]}}, {"name": "preview", "description": "Image preview feature.", "value": {"name": "PREVIEW", "type": "feature", "prompt": "Display a preview of the resized image before download."}}, {"name": "download", "description": "Download functionality.", "value": {"name": "DOWNLOAD", "type": "feature", "prompt": "Allow users to download resized images in various formats (JPEG, PNG, WebP)."}}, {"name": "authentication", "description": "User authentication system.", "value": {"name": "AUTH", "type": "feature", "prompt": "Implement user authentication to allow saving resized images to an account.", "linkers": [{"name": "service", "description": "Authentication service.", "value": {"name": "AUTH0", "type": "directive", "prompt": "Use Auth0 for managing user authentication and sessions."}}]}}]}}, {"name": "controls", "description": "User interface and security controls.", "value": {"name": "UI_CONTROLS", "type": "control", "prompt": "Define the user interface behavior and security controls.", "linkers": [{"name": "ui", "description": "User interface design.", "value": {"name": "RESPONSIVE_UI", "type": "control", "prompt": "Design a responsive and intuitive user interface using React components.", "linkers": [{"name": "library", "description": "UI component library.", "value": {"name": "MUI", "type": "directive", "prompt": "Use Material UI for consistent and modern UI components."}}]}}, {"name": "security", "description": "Security measures.", "value": {"name": "SECURITY", "type": "control", "prompt": "Implement security measures to protect user data and prevent malicious uploads.", "linkers": [{"name": "validation", "description": "File validation.", "value": {"name": "FILE_VALIDATION", "type": "directive", "prompt": "Validate uploaded files for size, type, and content before processing."}}, {"name": "sanitization", "description": "Data sanitization.", "value": {"name": "DATA_SANITIZATION", "type": "directive", "prompt": "Sanitize all user inputs and file metadata to prevent security vulnerabilities."}}]}}]}}]}]