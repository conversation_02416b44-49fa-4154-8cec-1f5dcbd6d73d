from typing import Literal


def write_to_hlc_file(
    object_name: str,
    mode: Literal["create", "change", "append"],
    linker_name: str | None = None,
    node: int | None = None,
) -> str:
    """Write an HLC object to HLC file.

    This tool writes an HLC object to the HLC.json file, in the specified mode.

    Args:
        object_name (str): The HLC object name.
        mode (Literal[&quot;create&quot;, &quot;change&quot;, &quot;append&quot;]): The
        mode to write in.
        linker_name (str | None, optional): The linker to use for linking the object to
        it's parent object if specified. Defaults to None.
        node (int | None, optional): The node to make write to when in change or append
        mode, should always specify the object to change or append to. Defaults to None.

    Returns:
        str: The HLC object as a string.

    """


def get_or_create_object(
    description: str,
    purpose: str,
    type: Literal[
        "entry_level", "top_level", "feature", "control", "directive", "literal"
    ],
) -> str:
    """_summary_

    _extended_summary_

    Args:
        description (str): _description_
        purpose (str): _description_
        type (Literal[ &quot;entry_level&quot;, &quot;top_level&quot;, &quot;feature&quot;, &quot;control&quot;, &quot;directive&quot;, &quot;literal&quot; ]): _description_

    Returns:
        str: _description_

    """


def get_or_create_linker(
    name: str,
    description: str,
    type: Literal["feature", "control", "directive", "literal"],
) -> str:
    """_summary_

    _extended_summary_

    Args:
        name (str): _description_
        description (str): _description_
        type (Literal[&quot;feature&quot;, &quot;control&quot;, &quot;directive&quot;, &quot;literal&quot;]): _description_

    Returns:
        str: _description_

    """
