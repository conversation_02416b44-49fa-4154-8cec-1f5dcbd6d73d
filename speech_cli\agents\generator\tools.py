from langgraph.config import get_stream_writer
from langgraph.types import interrupt


def ask_user_question(question: str) -> str:
    """Ask a question to the user and receive their response.

    This tool allows you to interact with the user by sending
    a question and wait for their reply.

    Args:
        question (str): The question to ask the user

    Returns:
        str: The user's response to the question

    """
    writer = get_stream_writer({"ask_user_question"})
    # Use LangGraph's interrupt mechanism to pause execution and request user input
    user_response = interrupt(question)

    # Return the user's response as a string
    return str(user_response) if user_response is not None else ""
